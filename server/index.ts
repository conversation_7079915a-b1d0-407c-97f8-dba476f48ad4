import express, { Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// =================================================================
// JOB MANAGEMENT TYPES AND STORAGE
// =================================================================

interface JobStatus {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  companyName: string;
  result?: object;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

// In-memory job storage (in production, consider Redis or similar)
const jobs = new Map<string, JobStatus>();

// Clean up old jobs (older than 1 hour)
const cleanupOldJobs = () => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  for (const [jobId, job] of jobs.entries()) {
    if (job.createdAt < oneHourAgo) {
      jobs.delete(jobId);
    }
  }
};

// Run cleanup every 30 minutes
setInterval(cleanupOldJobs, 30 * 60 * 1000);

// =================================================================
// CONFIGURATION
// =================================================================

/**
 * Loads, validates, and returns application configuration from environment variables.
 * Exits the process if a required variable is missing.
 */
const getAppConfig = () => {
  dotenv.config();

  const requiredEnvVars = ['LOGIN_EMAIL', 'LOGIN_PASSWORD', 'GEMINI_API_KEY'];

  for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
      console.error(`FATAL: Missing required environment variable: ${varName}`);
      process.exit(1); // Fail fast if configuration is incomplete
    }
  }

  return {
    port: process.env.PORT || 3001,
    login: {
      email: process.env.LOGIN_EMAIL!,
      password: process.env.LOGIN_PASSWORD!,
    },
    gemini: {
      apiKey: process.env.GEMINI_API_KEY!,
      modelId: process.env.MODEL_ID || 'gemini-2.5-flash', // Using a standard, recent model
    },
  };
};

const CONFIG = getAppConfig();

// =================================================================
// HELPER FUNCTIONS
// =================================================================

/**
 * Reads a prompt file from the './server' directory relative to the project root.
 * @param fileName The name of the file to read (e.g., 'system_prompt.md').
 * @returns The content of the file as a string.
 */
const readPromptFile = (fileName: string): string => {
  try {
    if (process.env.NODE_ENV === 'production') {
      const filePath = path.join(process.cwd(), 'prompts', fileName);
      return fs.readFileSync(filePath, 'utf8');
    }

    const filePath = path.join(process.cwd(), 'server', 'prompts', fileName);
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading prompt file at ${path.join('server', fileName)}:`, error);
    throw new Error(`Could not read instruction file: ${fileName}`);
  }
};

/**
 * Extracts a JSON object from a string, which may be wrapped in markdown code blocks.
 * @param text The string potentially containing the JSON.
 * @returns The parsed JavaScript object.
 */
const extractAndParseJson = (text: string): object => {
  // Regex to find JSON in a markdown block ```json ... ``` or as a raw object { ... }.
  const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```|(\{[\s\S]*\})/);

  if (!jsonMatch) {
    console.error("Raw text from AI that failed parsing:", text);
    throw new Error('No valid JSON object found in the response from the AI model.');
  }

  // The first capturing group is for the markdown block, the second for the raw object.
  const jsonString = jsonMatch[1] || jsonMatch[2];

  try {
    return JSON.parse(jsonString);
  } catch (parseError) {
    console.error('Failed to parse the following JSON string:', jsonString);
    throw new Error('The AI model returned a malformed JSON object.');
  }
};

/**
 * Calls the Google Gemini API with a given prompt and system instruction.
 * @param systemInstruction The system-level instructions for the model.
 * @param userPrompt The user's prompt for this specific task.
 * @returns A promise that resolves to the parsed JSON object from the model's response.
 */
const callGemini = async (systemInstruction: string, userPrompt: string): Promise<object> => {
  const { apiKey, modelId } = CONFIG.gemini;
  const url = `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent?key=${apiKey}`;

  const requestBody = {
    contents: [{ role: "user", parts: [{ text: userPrompt }] }],
    generationConfig: {
      temperature: 0.4,
      thinkingConfig: { thinkingBudget: 32768 },
    },
    system_instruction: { parts: [{ text: systemInstruction }] },
    tools: [{ urlContext: {} }, { googleSearch: {} }],
  };

  try {
    console.log(`Calling Gemini model ${modelId}...`);
    const response = await axios.post(url, requestBody, {
      headers: { 'Content-Type': 'application/json' }
    });

    const generatedContent = response.data.candidates?.[0]?.content?.parts?.[0]?.text;
    console.log("Raw Gemini Response:", generatedContent);

    if (!generatedContent) {
      console.error("Raw Gemini Response:", JSON.stringify(response.data));
      throw new Error('No content was generated by the Gemini API.');
    }

    return extractAndParseJson(generatedContent);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Axios error calling Gemini API:', error.response?.data || error.message);
    } else {
      console.error('Unexpected error in callGemini:', error);
    }
    // Re-throw the error to be caught by the route handler
    throw error;
  }
};

// =================================================================
// BACKGROUND PROCESSING
// =================================================================

/**
 * Processes company profiling in the background.
 */
const processCompanyProfilingInBackground = async (jobId: string, companyName: string) => {
  try {
    // Update job status to processing
    const job = jobs.get(jobId);
    if (!job) return;

    job.status = 'processing';
    job.updatedAt = new Date();
    jobs.set(jobId, job);

    console.log(`\n--- Starting background profiling for: ${companyName} (Job ID: ${jobId}) ---`);

    // Step 1: Generate the initial profile based on the system prompt.
    console.log("Step 1: Generating initial company profile.");
    const systemInstruction = readPromptFile('system_prompt.md');
    const initialUserPrompt = `**Company to Research**: ${companyName}\n\nPlease conduct thorough research and return the information in the exact JSON format specified in the system instructions.`;
    const initialProfile = await callGemini(systemInstruction, initialUserPrompt);

    // Step 2: Reflect on and enhance the generated profile.
    console.log("Step 2: Reflecting on and enhancing the profile.");
    const reflectionInstruction = readPromptFile('reflection_prompt.md');
    const reflectionUserPrompt = `Here is the initial company profile.
    \`\`\`json
    ${JSON.stringify(initialProfile, null, 2)}
    \`\`\`
    `;
    const finalProfile = await callGemini(reflectionInstruction, reflectionUserPrompt);

    // Update job with successful result
    job.status = 'completed';
    job.result = finalProfile;
    job.updatedAt = new Date();
    jobs.set(jobId, job);

    console.log(`--- Background profiling for ${companyName} completed successfully (Job ID: ${jobId}). ---`);
  } catch (error) {
    // Update job with error
    const job = jobs.get(jobId);
    if (job) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'An unknown error occurred';
      job.updatedAt = new Date();
      jobs.set(jobId, job);
    }

    console.error(`--- Background profiling for ${companyName} failed (Job ID: ${jobId}). ---`);
    console.error('Error:', error);
  }
};

// =================================================================
// ROUTE HANDLERS
// =================================================================

/**
 * Handles user login requests.
 */
const handleLogin = (req: Request, res: Response) => {
  const { email, password } = req.body;

  if (email === CONFIG.login.email && password === CONFIG.login.password) {
    res.json({
      success: true,
      message: 'Login successful',
      token: 'dummy-jwt-token', // In production, use a proper JWT library
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid email or password',
    });
  }
};

/**
 * Handles company profiling requests by starting a background job.
 */
const handleCompanyProfiling = async (req: Request, res: Response) => {
  const { companyName } = req.body;
  if (!companyName) {
    return res.status(400).json({ success: false, message: 'Company name is required' });
  }

  // Create a new job
  const jobId = uuidv4();
  const job: JobStatus = {
    id: jobId,
    status: 'pending',
    companyName: companyName.trim(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  jobs.set(jobId, job);

  // Start background processing (don't await)
  processCompanyProfilingInBackground(jobId, companyName.trim()).catch(error => {
    console.error('Background processing error:', error);
  });

  // Return immediately with job ID
  res.json({
    success: true,
    jobId: jobId,
    message: 'Company profiling started. Use the job ID to check status.',
  });
};

/**
 * Handles job status requests.
 */
const handleJobStatus = (req: Request, res: Response) => {
  const { jobId } = req.params;

  if (!jobId) {
    return res.status(400).json({ success: false, message: 'Job ID is required' });
  }

  const job = jobs.get(jobId);

  if (!job) {
    return res.status(404).json({ success: false, message: 'Job not found' });
  }

  // Return job status without internal details
  const response: {
    success: boolean;
    jobId: string;
    status: string;
    companyName: string;
    createdAt: Date;
    updatedAt: Date;
    data?: object;
    error?: string;
  } = {
    success: true,
    jobId: job.id,
    status: job.status,
    companyName: job.companyName,
    createdAt: job.createdAt,
    updatedAt: job.updatedAt,
  };

  if (job.status === 'completed' && job.result) {
    response.data = job.result;
  }

  if (job.status === 'failed' && job.error) {
    response.error = job.error;
  }

  res.json(response);
};

/**
 * Handles health check requests.
 */
const handleHealthCheck = (_req: Request, res: Response) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
};

// =================================================================
// EXPRESS APP SETUP
// =================================================================

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// API Routes
app.post('/login', handleLogin);
app.post('/company_profiling', handleCompanyProfiling);
app.get('/job/:jobId', handleJobStatus);
app.get('/health', handleHealthCheck);

// =================================================================
// SERVER START
// =================================================================

app.listen(CONFIG.port, () => {
  console.log(`Server running on port ${CONFIG.port}`);
});
